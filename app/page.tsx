
import PickerContainer from '@/app/ui/picker-container'
import Latest from './ui/latest';
import { Suspense } from 'react';
import { LatestSkeleton } from './ui/skeletons';

export default async function Home({
  searchParams,
}: {
  searchParams?: { [key: string]: string | string[] | undefined };
}) {
  return (
    <div className="font-sans grid grid-rows-[20px_1fr_20px] items-center justify-items-center min-h-screen p-8 pb-20 gap-16 sm:p-20">
      <main className="flex flex-col gap-[32px] row-start-2 items-center sm:items-start">
        <Suspense fallback={<LatestSkeleton />}>
          <Latest />
        </Suspense>
        <PickerContainer searchParams={searchParams} />
      </main>
    </div>
  );
}

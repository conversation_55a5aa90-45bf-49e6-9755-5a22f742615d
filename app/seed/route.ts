import postgres from 'postgres';
import { nums } from '@/app/lib/placeholder-data';

const sql = postgres(process.env.POSTGRES_URL!, { ssl: 'require', prepare: false });

async function seedNums() {
    await sql`
        CREATE TABLE IF NOT EXISTS nums (
          code VARCHAR(16) PRIMARY KEY,
          week VARCHAR(16) NOT NULL,
          red VARCHAR(32) NOT NULL,
          blue VARCHAR(16) NOT NULL,
          sales BIGINT NOT NULL,
          poolmoney BIGINT NOT NULL,
          content TEXT NOT NULL,
          prizegrades JSON NOT NULL,
          date DATE NOT NULL
        );
  `;

    const insertedNums = await Promise.all(
        nums.map((num: any) => (sql`INSERT INTO nums VALUES (
            ${num.code},
            ${num.week},
            ${num.red},
            ${num.blue},
            ${num.sales},
            ${num.poolmoney},
            ${num.content},
            ${JSON.stringify(num.prizegrades)},
            ${num.date}
          ) ON CONFLICT DO NOTHING;
        `)),
    );
    return insertedNums;
}

export async function GET() {
    try {
        const result = await sql.begin((sql) => [
            seedNums(),
        ]);
        return Response.json({ message: 'Database seeded successfully' });
    } catch (error) {
        return Response.json({ error }, { status: 500 });
    }
}
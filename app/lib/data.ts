import postgres from 'postgres';
import { Nums } from '@/app/lib/definitions'
const sql = postgres(process.env.POSTGRES_URL!, { ssl: 'require' });
export async function fetchLatestNums() {
    try {
        const data = await sql<Nums[]>`
      SELECT *
      FROM nums
      ORDER BY nums.date DESC
      LIMIT 1`;
        return data[0];
    } catch (error) {
        console.error('Database Error:', error);
        throw new Error('Failed to fetch the latest nums.');
    }
}
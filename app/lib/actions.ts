'use server';

import postgres from 'postgres';

const sql = postgres(process.env.POSTGRES_URL!, { ssl: 'require' });

export async function queryNums(reds: number[], blue: number) {
    const redsStr = reds.join(',');
    try {
        const data = await sql`
            SELECT *
            FROM nums
            WHERE red = ${redsStr} ;
        `;
        console.log(`data:${data}`);
        return data;
    }
    catch (error) {
        console.error('Database Error:', error);
    }
}
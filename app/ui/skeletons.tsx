export function BallSkeleton() {
    return (
        <div className="w-12 h-12 bg-gray-300 rounded-full animate-pulse m-1"></div>
    );
}

export function LatestSkeleton() {
    return (
        <div className="flex flex-col p-4 rounded-xl bg-gray-50 shadow-sm">
            <div className=' flex flex-row'>
                {[...Array(6)].map((_, i) => <BallSkeleton key={i} />)}
                <span className='justify'>|</span>
                <BallSkeleton />
            </div>
            <div className="h-4 bg-gray-300 rounded animate-pulse"></div>
        </div>
    );
}
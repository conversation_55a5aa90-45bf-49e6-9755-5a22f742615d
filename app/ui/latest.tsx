import { fetchLatestNums } from '@/app/lib/data';
import Ball from './ball';
export default async function Latest() {
    const lastestNums = await fetchLatestNums();
    console.log(`latestNums:${lastestNums.prizegrades}`)
    return (<div className='flex flex-col p-4 rounded-xl bg-gray-50 shadow-sm'>
        <div className=' flex flex-row'>
            {lastestNums.red.split(',').map((num) => (<Ball key={Number.parseInt(num)} number={Number.parseInt(num)} type='red' selected={true} />))}
            <span className='justify'>|</span>
            {<Ball key={Number.parseInt(lastestNums.blue)} number={Number.parseInt(lastestNums.blue)} type='blue' selected={true} />}
        </div>
        {lastestNums.content}
    </div>);
}

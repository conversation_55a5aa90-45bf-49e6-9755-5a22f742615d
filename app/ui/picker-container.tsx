import { queryNums } from '../lib/actions';
import PickerClient from '@/app/ui/picker-client';

interface PickerContainerProps {
  searchParams?: { [key: string]: string | string[] | undefined };
}

export default async function PickerContainer({ searchParams }: PickerContainerProps) {
  const resolvedSearchParams = await searchParams;
  const redsParam = resolvedSearchParams?.reds;
  const blueParam = resolvedSearchParams?.blue;

  let queryResult: any = null;

  if (redsParam && blueParam) {
    const reds = Array.isArray(redsParam) ? redsParam.map(Number) : [Number(redsParam)];
    const blue = Number(blueParam);

    if (reds.length === 6 && !isNaN(blue)) {
      queryResult = await queryNums(reds, blue);
    }
  }

  return <PickerClient initialQueryResult={queryResult} />;
}

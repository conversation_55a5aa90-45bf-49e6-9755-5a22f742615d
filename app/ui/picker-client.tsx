'use client';
import Ball from '@/app/ui/ball';
import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

function Result({ reds, blue, queryResult }: { reds: number[]; blue: number | null; queryResult: any }) {
    return (<div className='flex flex-col gap-2'>
        <div className='flex flex-row items-center'>
            {reds.map((num) => (
                <Ball key={num} number={num} type='red' selected={true} />
            ))}
            {blue && <Ball key={blue} number={blue} type='blue' selected={true} />}
        </div>
        {queryResult && (
            <div className="mt-2 p-2 bg-gray-100 rounded">
                <p>查询结果:</p>
                <pre>{JSON.stringify(queryResult, null, 2)}</pre>
            </div>
        )}
    </div>);
}

export default function PickerClient({ initialQueryResult }: { initialQueryResult: any }) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const redBallNums = Array.from({ length: 36 }, (_, i) => i + 1);
    const blueBallNums = Array.from({ length: 16 }, (_, i) => i + 1);
    const [selectedRedBalls, setSelectedRedBalls] = useState<number[]>([]);
    const [selectedBlueBall, setSelectedBlueBall] = useState<number | null>(null);
    const [queryResult, setQueryResult] = useState(initialQueryResult);

    useEffect(() => {
        // Sync with URL search params on initial load or when URL changes
        const redsParam = searchParams.get('reds');
        const blueParam = searchParams.get('blue');
        if (redsParam && blueParam) {
            const reds = redsParam.split(',').map(Number);
            const blue = Number(blueParam);
            if (reds.length === 6 && !isNaN(blue)) {
                setSelectedRedBalls(reds);
                setSelectedBlueBall(blue);
            }
        }
    }, [searchParams]);


    // 处理红球点击
    const handleRedBallClick = (num: number) => {
        setSelectedRedBalls(prev => {
            if (prev.includes(num)) {
                return prev.filter(ball => ball !== num);
            } else {
                if (prev.length < 6) {
                    return [...prev, num];
                }
                return prev;
            }
        });
    };

    // 处理蓝球点击
    const handleBlueBallClick = (num: number) => {
        setSelectedBlueBall(prev => {
            if (prev === num) {
                return null;
            } else {
                return num;
            }
        });
    };

    const handleSubmit = () => {
        if (selectedRedBalls.length === 6 && selectedBlueBall !== null) {
            const redsStr = selectedRedBalls.join(',');
            // This will cause a navigation, which will trigger the server component to fetch data
        }
    };

    return (
        <div className="flex flex-col gap-4 p-4 rounded-xl bg-gray-50 shadow-sm">
            <div>
                <h2 className="text-xl font-bold mb-2">
                    选择六个红球 ({selectedRedBalls.length}/6)
                </h2>
                <div className='flex flex-row flex-wrap gap-1'>
                    {
                        redBallNums.map((num) => (
                            <Ball
                                key={num}
                                number={num}
                                type='red'
                                selected={selectedRedBalls.includes(num)}
                                onClick={() => handleRedBallClick(num)}
                            />
                        ))
                    }
                </div>
            </div>

            <div>
                <h2 className="text-xl font-bold mb-2">
                    选择一个蓝球 ({selectedBlueBall ? '1' : '0'}/1)
                </h2>
                <div className='flex flex-row flex-wrap gap-1'>
                    {
                        blueBallNums.map((num) => (
                            <Ball
                                key={num}
                                number={num}
                                type='blue'
                                selected={selectedBlueBall === num}
                                onClick={() => handleBlueBallClick(num)}
                            />
                        ))
                    }
                </div>
            </div>

            {/* 显示当前选择和查询按钮 */}
            <div className='flex flex-col items-start'>
                <Result reds={selectedRedBalls} blue={selectedBlueBall} queryResult={queryResult} />
                {(selectedRedBalls.length == 6) && selectedBlueBall &&
                    <button
                        onClick={handleSubmit}
                        className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
                    >
                        查询
                    </button>}
            </div>
        </div>
    );
}

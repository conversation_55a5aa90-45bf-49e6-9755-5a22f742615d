import { BallProps } from '@/app/lib/definitions';

export default function Ball({ number, type, selected, onClick }: BallProps) {
    const bgColor = selected ?
        type === 'red' ? 'bg-red-500' : 'bg-blue-500' :
        type === 'red' ? 'bg-red-300' : 'bg-blue-300';

    return (
        <button
            className={`text-3xl font-bold ${bgColor} text-white rounded-full w-12 h-12 flex items-center justify-center cursor-pointer hover:opacity-80 transition-opacity m-1`}
            onClick={onClick}
        >
            {number}
        </button>
    );
}
